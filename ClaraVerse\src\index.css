@import url('https://fonts.googleapis.com/css2?family=Quicksand:wght@400;500;600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --sakura-50: #fef6f9;
    --sakura-100: #fee3ec;
    --sakura-200: #ffc6da;
    --sakura-300: #ff9dc1;
    --sakura-400: #ff669d;
    --sakura-500: #ff1a75;
    --sakura-600: #e6006b;
    font-family: 'Quicksand', sans-serif;
  }

  .dark {
    --sakura-50: #1a0f13;
    --sakura-100: #2a1820;
    --sakura-200: #3d1f2d;
    --sakura-300: #4f2639;
    --sakura-400: #662d46;
    --sakura-500: #ff1a75;
    --sakura-600: #ff3385;
  }
}

@layer components {
  .prose-sakura {
    @apply prose dark:prose-invert;
  }

  .prose-sakura a {
    @apply text-sakura-500 transition-colors;
  }

  .prose-sakura a:hover {
    color: var(--sakura-600);
  }

  .dark .prose-sakura a {
    @apply text-sakura-400;
  }

  .dark .prose-sakura a:hover {
    @apply text-sakura-500;
  }

  .glassmorphic {
    @apply bg-white/70 backdrop-blur-lg border border-white/20 dark:bg-gray-900/70 dark:border-gray-800/20;
  }

  .glassmorphic-card {
    @apply bg-white/60 dark:bg-gray-900/60 backdrop-blur-md border border-white/30 dark:border-gray-700/30 shadow-xl;
  }
}

/* Complete cleanup of code blocks in markdown for both light and dark mode */
.prose pre, 
.prose code,
.dark .prose pre,
.dark .prose code {
  border: none !important;
  box-shadow: none !important;
}

/* Completely remove code syntax highlighting for all spans */
.prose pre code span,
.dark .prose pre code span {
  background: transparent !important;
  color: #e5e7eb !important;
  border: none !important;
  font-weight: normal !important;
  text-decoration: none !important;
}

/* Make code blocks consistent in both modes */
.prose pre,
.dark .prose pre {
  background-color: #1E1E1E !important;
  color: #e5e7eb !important;
  border-radius: 4px !important;
  padding: 0.75rem 1rem !important;
  border: none !important;
}

/* Ensure code text is visible in both modes with no highlighting */
.prose pre code,
.dark .prose pre code {
  color: #e5e7eb !important;
  background-color: transparent !important;
  padding: 0 !important;
}

/* Inline code styling for both modes */
.prose :not(pre) > code,
.dark .prose :not(pre) > code {
  background-color: #f3f4f6 !important;
  color: #1f2937 !important;
  padding: 0.2em 0.4em !important;
  border-radius: 0.25rem !important;
  border: none !important;
}

/* Dark mode inline code */
.dark .prose :not(pre) > code {
  background-color: #374151 !important;
  color: #f9fafb !important;
}

/* Override any theme-specific syntax highlighting */
.prose pre [class*="language-"],
.prose code [class*="language-"],
.dark .prose pre [class*="language-"],
.dark .prose code [class*="language-"] {
  color: #e5e7eb !important;
  background: transparent !important;
  text-shadow: none !important;
  font-family: monospace !important;
}

/* Smooth width transitions */
.w-0 {
  width: 0;
}

.w-auto {
  width: auto;
}

/* Scrollbar styling */
.scrollbar-thin {
  scrollbar-width: thin;
}

.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  @apply bg-transparent;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  @apply bg-gray-400/50 dark:bg-gray-600/50 rounded-full;
}

/* Smooth scrolling */
.scroll-smooth {
  scroll-behavior: smooth;
}

/* Animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.3s ease-out forwards;
}

/* Animation delay utilities */
.animation-delay-100 {
  animation-delay: 100ms;
}

.animation-delay-200 {
  animation-delay: 200ms;
}

.animation-delay-300 {
  animation-delay: 300ms;
}

/* App runner animations */
@keyframes slideInUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes pulse-soft {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

.animate-slide-up {
  animation: slideInUp 0.4s ease-out forwards;
}

.animate-pulse-soft {
  animation: pulse-soft 2s infinite ease-in-out;
}

/* Auto-resize textarea */
textarea {
  min-height: 2.5rem;
  transition: height 0.2s ease;
}

/* Dark mode specific styles for app runner */
.dark .app-runner-container {
  color-scheme: dark;
}

/* Ensure markdown content has proper colors in dark mode */
.dark .markdown-wrapper {
  color-scheme: dark;
}

.dark-mode-prose {
  color-scheme: dark;
}

.dark .dark-mode-prose {
  --tw-prose-body: #e5e7eb;
  --tw-prose-headings: #f9fafb;
  --tw-prose-lead: #d1d5db;
  --tw-prose-links: #93c5fd;
  --tw-prose-bold: #f9fafb;
  --tw-prose-counters: #d1d5db;
  --tw-prose-bullets: #d1d5db;
  --tw-prose-hr: #374151;
  --tw-prose-quotes: #f3f4f6;
  --tw-prose-quote-borders: #374151;
  --tw-prose-captions: #9ca3af;
  --tw-prose-code: #f9fafb;
  --tw-prose-pre-code: #e5e7eb;
  --tw-prose-pre-bg: #1f2937;
  --tw-prose-th-borders: #374151;
  --tw-prose-td-borders: #374151;
}

/* Code blocks in dark mode */
.dark .prose pre {
  background-color: #1f2937;
  border: 1px solid #374151;
}

.dark .prose code {
  color: #f9fafb;
  background-color: #374151;
  padding: 0.2em 0.4em;
  border-radius: 0.25rem;
  font-weight: 500;
}

.dark .prose a {
  color: #93c5fd;
  text-decoration: underline;
}

/* Lists in dark mode */
.dark .prose ul, 
.dark .prose ol {
  color: #e5e7eb;
}

/* Table styling in dark mode */
.dark .prose table {
  border-color: #374151;
}

.dark .prose thead {
  color: #f9fafb;
  border-bottom-color: #4b5563;
}

.dark .prose tbody tr {
  border-bottom-color: #374151;
}

.prose h2 {
  @apply text-2xl font-bold mb-4 text-gray-900 dark:text-white mt-8;
}

.prose h3 {
  @apply text-xl font-semibold mb-3 text-gray-800 dark:text-gray-100 mt-6;
}

.prose ol {
  @apply list-decimal list-inside mb-4 space-y-2;
}

.prose ul {
  @apply list-disc list-inside mb-4 space-y-2;
}

.prose p {
  @apply mb-4 text-gray-700 dark:text-gray-300;
}

/* Complete cleanup of code blocks in markdown for both light and dark mode */
.bg-gradient-to-br {
  background: linear-gradient(to bottom right, #ffffff, #fee3ec);
}

.dark .bg-gradient-to-br {
  background: #000000;
}

/* Sidebar icon transitions */
.sidebar-icon {
  opacity: 0;
  transition: opacity 0.2s ease-in-out;
  width: 20px;
  height: 20px;
}

.sidebar-expanded .sidebar-icon,
.glassmorphic:hover .sidebar-icon {
  opacity: 1;
}

/* Hide scrollbar but maintain functionality */
.scrollbar-none {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.scrollbar-none::-webkit-scrollbar {
  display: none; /* Chrome, Safari and Opera */
}