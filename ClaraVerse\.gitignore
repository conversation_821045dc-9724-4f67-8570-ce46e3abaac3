# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
dist
dist-ssr
*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
.env
.env.*
!.env.example
/tools/.env

# Security files
secret_key.txt
*_key.txt
*.pem
*.key

# Electron specific
release
.electron-builder
electron-builder.yml
*.dmg
*.exe
*.deb
*.AppImage
*.snap
*.blockmap
*.zip
latest-mac.yml
latest-linux.yml
latest-win.yml
.bolt
__pycache__


# Code signing files
package-mac-signed.sh
certificate.p12

./Support
python-env
.clara

./py_backend/__pycache__
./py_backend/cache
./py_backend/python-env
./py_backend/python-env/cache
./py_backend/python-env/pip-cache
./py_backend/python-env/python-env

.cursor

# any files in tools
tools/*

/clara_interpreter_dockerstuff

